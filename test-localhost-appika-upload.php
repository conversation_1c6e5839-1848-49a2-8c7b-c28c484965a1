<?php
/**
 * Test Localhost Appika Upload Issue
 * Diagnose why <PERSON><PERSON><PERSON> upload works on server but not on localhost
 */

echo "<h1>🔍 Localhost Appika Upload Diagnostic</h1>";
echo "<p>Testing why Appika customer upload works on server but fails on localhost...</p>";

// Auto-detect environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

echo "<h2>1. Environment Detection</h2>";
echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Current Environment:</strong> " . ($is_localhost ? 'LOCALHOST' : 'SERVER') . "<br>";
echo "<strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>File Base Path:</strong> '" . $file_base_path . "'<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "</div>";

// Include required files
try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
    echo "✅ Composer autoload included successfully<br>";
} catch (Exception $e) {
    echo "❌ Failed to include Composer autoload: " . $e->getMessage() . "<br>";
    exit();
}

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php';
    echo "✅ Database connection included successfully<br>";
} catch (Exception $e) {
    echo "❌ Failed to include database connection: " . $e->getMessage() . "<br>";
    exit();
}

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php';
    echo "✅ API configuration included successfully<br>";
} catch (Exception $e) {
    echo "❌ Failed to include API configuration: " . $e->getMessage() . "<br>";
    exit();
}

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/create-customer-minimal.php';
    echo "✅ Customer creation functions included successfully<br>";
} catch (Exception $e) {
    echo "❌ Failed to include customer creation functions: " . $e->getMessage() . "<br>";
    exit();
}

echo "<h2>2. API Configuration Test</h2>";
$apiConfig = getCustomerApiConfig();
echo "<div style='background: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>API Endpoint:</strong> " . $apiConfig['endpoint'] . "<br>";
echo "<strong>API Path:</strong> " . $apiConfig['path'] . "<br>";
echo "<strong>API Key:</strong> " . substr($apiConfig['key'], 0, 20) . "...<br>";
echo "</div>";

echo "<h2>3. Network Connectivity Test</h2>";

// Test basic connectivity to Appika API
$client = new \GuzzleHttp\Client([
    'timeout' => 10,
    'connect_timeout' => 5,
    'http_errors' => false,
]);

try {
    echo "Testing connection to Appika API...<br>";
    $response = $client->request('GET', $apiConfig['endpoint'] . '/health', [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
        ]
    ]);
    
    $statusCode = $response->getStatusCode();
    echo "✅ Connection successful - Status: $statusCode<br>";
    
} catch (\GuzzleHttp\Exception\ConnectException $e) {
    echo "❌ Connection failed: " . $e->getMessage() . "<br>";
    echo "<strong>Possible causes:</strong><br>";
    echo "- Localhost firewall blocking outbound connections<br>";
    echo "- XAMPP/localhost environment restrictions<br>";
    echo "- Network connectivity issues<br>";
} catch (Exception $e) {
    echo "❌ Request failed: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Test Customer Creation API Call</h2>";

// Test actual customer creation
$testCustomerData = [
    'no' => 'TEST_' . time(),
    'name' => 'Test Customer',
    'entity_type' => '1',
    'grp_id' => '10',
    'ofc_id' => '511',
    'assign2' => '1',
    'creator' => '1',
    'start_date' => date('Y-m-d'),
    'status' => 'a'
];

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Test Data:</strong><br>";
echo "<pre>" . json_encode($testCustomerData, JSON_PRETTY_PRINT) . "</pre>";
echo "</div>";

try {
    echo "Sending test customer creation request...<br>";
    
    $response = $client->request('POST', $apiConfig['endpoint'] . $apiConfig['path'], [
        'headers' => [
            'X-api-key' => $apiConfig['key'],
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ],
        'json' => $testCustomerData
    ]);
    
    $statusCode = $response->getStatusCode();
    $body = $response->getBody()->getContents();
    $responseData = json_decode($body, true);
    
    echo "<div style='background: " . ($statusCode >= 200 && $statusCode < 300 ? '#d4edda' : '#f8d7da') . "; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Response Status:</strong> $statusCode<br>";
    echo "<strong>Response Body:</strong><br>";
    echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
    if ($statusCode >= 200 && $statusCode < 300 && isset($responseData[0]['id'])) {
        echo "✅ <strong>SUCCESS:</strong> Customer created successfully in Appika API<br>";
        echo "Customer ID: " . $responseData[0]['id'] . "<br>";
        
        // Clean up test customer
        echo "<br>Cleaning up test customer...<br>";
        try {
            $deleteResponse = $client->request('DELETE', $apiConfig['endpoint'] . $apiConfig['path'] . '/' . $responseData[0]['id'], [
                'headers' => [
                    'X-api-key' => $apiConfig['key'],
                    'Accept' => 'application/json',
                ]
            ]);
            echo "✅ Test customer cleaned up<br>";
        } catch (Exception $e) {
            echo "⚠️ Could not clean up test customer: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ <strong>FAILED:</strong> Customer creation failed<br>";
        echo "This explains why localhost uploads don't work!<br>";
    }
    
} catch (\GuzzleHttp\Exception\ConnectException $e) {
    echo "❌ <strong>CONNECTION ERROR:</strong> " . $e->getMessage() . "<br>";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>This is likely the root cause!</strong><br>";
    echo "Localhost environments often have restrictions on outbound HTTP requests.<br>";
    echo "<strong>Common solutions:</strong><br>";
    echo "1. Check XAMPP/localhost firewall settings<br>";
    echo "2. Verify internet connectivity from localhost<br>";
    echo "3. Check if antivirus is blocking outbound connections<br>";
    echo "4. Try using a different network or VPN<br>";
    echo "</div>";
} catch (Exception $e) {
    echo "❌ <strong>REQUEST ERROR:</strong> " . $e->getMessage() . "<br>";
}

echo "<h2>5. Localhost vs Server Comparison</h2>";
echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Why it works on server but not localhost:</h3>";
echo "<ul>";
echo "<li><strong>Server:</strong> Has unrestricted outbound internet access</li>";
echo "<li><strong>Localhost:</strong> May have firewall/network restrictions</li>";
echo "<li><strong>Server:</strong> Professional hosting environment</li>";
echo "<li><strong>Localhost:</strong> Development environment with security restrictions</li>";
echo "</ul>";

echo "<h3>Common localhost restrictions:</h3>";
echo "<ul>";
echo "<li>Windows Firewall blocking outbound connections</li>";
echo "<li>Antivirus software blocking HTTP requests</li>";
echo "<li>XAMPP security settings</li>";
echo "<li>Router/network firewall restrictions</li>";
echo "<li>ISP blocking certain outbound connections</li>";
echo "</ul>";
echo "</div>";

echo "<h2>6. Debugging Steps</h2>";
echo "<div style='background: #e8f0ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>To fix localhost Appika uploads:</h3>";
echo "<ol>";
echo "<li><strong>Check Windows Firewall:</strong> Allow PHP/Apache outbound connections</li>";
echo "<li><strong>Check Antivirus:</strong> Whitelist XAMPP directory</li>";
echo "<li><strong>Test with curl:</strong> <code>curl -X GET https://dev-api-pooh-sgsg.appika.com</code></li>";
echo "<li><strong>Check error logs:</strong> Look in PHP error logs for connection errors</li>";
echo "<li><strong>Try different network:</strong> Mobile hotspot or different WiFi</li>";
echo "<li><strong>Check XAMPP settings:</strong> Ensure no proxy/security restrictions</li>";
echo "</ol>";
echo "</div>";

echo "<h2>7. Error Log Check</h2>";
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    echo "PHP Error Log: $error_log_file<br>";
    $recent_errors = shell_exec("tail -20 '$error_log_file' 2>/dev/null");
    if ($recent_errors) {
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px;'>";
        echo "<strong>Recent PHP Errors:</strong><br>";
        echo "<pre>" . htmlspecialchars($recent_errors) . "</pre>";
        echo "</div>";
    }
} else {
    echo "PHP Error Log not found or not accessible<br>";
}

echo "<h2>8. Summary</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>The issue is likely network connectivity from localhost to Appika API.</strong></p>";
echo "<p>The code is identical between localhost and server, but localhost environments often have network restrictions that prevent outbound API calls.</p>";
echo "<p><strong>Next steps:</strong> Check the network connectivity test results above and follow the debugging steps to resolve the connection issue.</p>";
echo "</div>";

?>
