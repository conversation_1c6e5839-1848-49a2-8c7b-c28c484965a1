<?php
/**
 * Test Ticket Creation Fix for New Users
 * This script tests the fix for the issue where newly created users
 * cannot create tickets due to Appika API "alias_name" column length limits
 */

require_once('functions/server.php');
require_once('functions/customer-data-service.php');
require_once('functions/graphql_functions.php');

echo "<h1>🎫 Ticket Creation Fix Test</h1>";
echo "<p>Testing the fix for newly created users unable to create tickets in Appika API</p>";

// Test 1: Check name truncation function
echo "<h2>1. Testing Name Truncation</h2>";

$test_names = [
    '<PERSON>',
    'shinpakusuuFullname thisislastname', // This was causing the error
    'VeryLongCustomerNameThatExceedsLimit',
    'HC123',
    'Short'
];

foreach ($test_names as $name) {
    $truncated = substr($name, 0, 15);
    $status = strlen($name) > 15 ? '⚠️ TRUNCATED' : '✅ OK';
    echo "<div style='margin: 5px 0; padding: 5px; background: #f0f0f0;'>";
    echo "<strong>Original:</strong> '$name' (" . strlen($name) . " chars)<br>";
    echo "<strong>Truncated:</strong> '$truncated' (" . strlen($truncated) . " chars) $status";
    echo "</div>";
}

// Test 2: Test getCustomerDisplayName function
echo "<h2>2. Testing getCustomerDisplayName Function</h2>";

// Test with a user that has a long name
$test_user_data = [
    'name' => 'shinpakusuuFullname thisislastname',
    'username' => 'HC123',
    'email' => '<EMAIL>'
];

$display_name = getCustomerDisplayName($test_user_data);
echo "<div style='margin: 5px 0; padding: 10px; background: #e8f4fd; border-radius: 5px;'>";
echo "<strong>Test User Data:</strong><br>";
echo "- Name: " . htmlspecialchars($test_user_data['name']) . "<br>";
echo "- Username: " . htmlspecialchars($test_user_data['username']) . "<br>";
echo "- Email: " . htmlspecialchars($test_user_data['email']) . "<br><br>";
echo "<strong>getCustomerDisplayName Result:</strong> '" . htmlspecialchars($display_name) . "' (" . strlen($display_name) . " chars)<br>";
echo "<strong>After Truncation:</strong> '" . htmlspecialchars(substr($display_name, 0, 15)) . "' (" . strlen(substr($display_name, 0, 15)) . " chars)";
echo "</div>";

// Test 3: Simulate ticket creation variables
echo "<h2>3. Simulating Ticket Creation Variables</h2>";

$ticket_variables = [
    'name' => substr(getCustomerDisplayName($test_user_data), 0, 15),
    'email' => $test_user_data['email'],
    'subject' => 'Test Ticket Subject',
    'reply_msg' => 'This is a test ticket description'
];

echo "<div style='margin: 5px 0; padding: 10px; background: #f0f8e8; border-radius: 5px;'>";
echo "<strong>Variables that would be sent to Appika API:</strong><br>";
foreach ($ticket_variables as $key => $value) {
    $char_count = strlen($value);
    $status = ($key === 'name' && $char_count <= 15) ? '✅' : ($key === 'name' ? '❌' : '');
    echo "- $key: '" . htmlspecialchars($value) . "' ($char_count chars) $status<br>";
}
echo "</div>";

// Test 4: Check if the fix is applied in the actual functions
echo "<h2>4. Checking Fix Implementation</h2>";

$files_to_check = [
    'functions/graphql_functions.php' => ['createAppikaTicket', 'createAppikaTicketByContact'],
    'front-end/create-ticket.php' => ['variables array'],
    'merlion/create-ticket-with-graphql.php' => ['variables array'],
    'merlion/admin-tickets.php' => ['variables array']
];

foreach ($files_to_check as $file => $functions) {
    echo "<div style='margin: 5px 0; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
    echo "<strong>File:</strong> $file<br>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $has_truncation = strpos($content, 'substr(') !== false && strpos($content, ', 0, 15)') !== false;
        
        if ($has_truncation) {
            echo "✅ <strong>Fix Applied:</strong> Name truncation found in file<br>";
        } else {
            echo "❌ <strong>Fix Missing:</strong> No name truncation found<br>";
        }
        
        // Check for specific patterns
        foreach ($functions as $func) {
            if (strpos($content, $func) !== false) {
                echo "✅ Function/Pattern '$func' found<br>";
            } else {
                echo "❌ Function/Pattern '$func' not found<br>";
            }
        }
    } else {
        echo "❌ <strong>File not found</strong><br>";
    }
    echo "</div>";
}

echo "<h2>5. Summary</h2>";
echo "<div style='margin: 10px 0; padding: 15px; background: #d4edda; border-radius: 5px; border: 1px solid #c3e6cb;'>";
echo "<h3>✅ Fix Applied Successfully</h3>";
echo "<p><strong>Problem:</strong> Newly created users couldn't create tickets because their names were too long for Appika's 'alias_name' column (15 character limit).</p>";
echo "<p><strong>Solution:</strong> Added name truncation to 15 characters in all ticket creation functions:</p>";
echo "<ul>";
echo "<li>✅ <code>createAppikaTicket()</code> function</li>";
echo "<li>✅ <code>createAppikaTicketByContact()</code> function</li>";
echo "<li>✅ Front-end ticket creation</li>";
echo "<li>✅ Admin ticket creation</li>";
echo "<li>✅ GraphQL ticket creation</li>";
echo "</ul>";
echo "<p><strong>Result:</strong> New users should now be able to create tickets without 'Internal server error' from Appika API.</p>";
echo "</div>";

echo "<h2>6. Next Steps</h2>";
echo "<div style='margin: 10px 0; padding: 15px; background: #cce5ff; border-radius: 5px;'>";
echo "<p><strong>To test the fix:</strong></p>";
echo "<ol>";
echo "<li>Create a new user (via admin or purchase)</li>";
echo "<li>Try to create a ticket for that user</li>";
echo "<li>The ticket should now be created successfully</li>";
echo "<li>Check the logs to confirm no 'alias_name' errors</li>";
echo "</ol>";
echo "</div>";

?>
