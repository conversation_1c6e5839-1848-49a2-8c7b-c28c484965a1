<?php
/**
 * Test Safe Path Fix
 * Verify that the improved path detection works on both localhost and server
 */

echo "<h1>🔧 Safe Path Fix Test</h1>";
echo "<p>Testing the improved conditional includes that work on both localhost and server...</p>";

// Auto-detect environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

echo "<h2>1. Environment Detection</h2>";
echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Current Environment:</strong> " . ($is_localhost ? 'LOCALHOST' : 'SERVER') . "<br>";
echo "<strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>File Base Path:</strong> '" . $file_base_path . "'<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "</div>";

echo "<h2>2. Testing Path Resolution</h2>";
$test_paths = [
    'vendor/autoload.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php',
    'config/api-config.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php',
    'functions/server.php' => __DIR__ . '/server.php',
    'functions/customer-data-service.php' => __DIR__ . '/customer-data-service.php'
];

foreach ($test_paths as $name => $path) {
    $exists = file_exists($path);
    $status = $exists ? '✅' : '❌';
    echo "$status <strong>$name:</strong> $path<br>";
    if (!$exists) {
        echo "&nbsp;&nbsp;&nbsp;&nbsp;⚠️ This file is missing and will cause the Appika upload to fail!<br>";
    }
}

echo "<h2>3. Testing Improved create-customer-minimal.php</h2>";

// Simulate what happens when payment-success.php includes the files
echo "<h3>Simulating payment-success.php includes:</h3>";

// First, include the files like payment-success.php does
$files_included_first = [];
try {
    if (file_exists($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php')) {
        require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
        $files_included_first[] = 'vendor/autoload.php';
        echo "✅ vendor/autoload.php included (like payment-success.php)<br>";
    }
    
    if (file_exists(__DIR__ . '/server.php')) {
        include_once(__DIR__ . '/server.php');
        $files_included_first[] = 'server.php';
        echo "✅ server.php included (like payment-success.php)<br>";
    }
    
    if (file_exists(__DIR__ . '/customer-data-service.php')) {
        require_once __DIR__ . '/customer-data-service.php';
        $files_included_first[] = 'customer-data-service.php';
        echo "✅ customer-data-service.php included (like payment-success.php)<br>";
    }
} catch (Exception $e) {
    echo "❌ Error in first includes: " . $e->getMessage() . "<br>";
}

echo "<h3>Now testing create-customer-minimal.php (should not cause conflicts):</h3>";

try {
    require_once __DIR__ . '/create-customer-minimal.php';
    echo "✅ create-customer-minimal.php included successfully<br>";
    
    // Test if all required functions are available
    if (function_exists('createCustomerMinimal')) {
        echo "✅ createCustomerMinimal function is available<br>";
    } else {
        echo "❌ createCustomerMinimal function is NOT available<br>";
    }
    
    if (function_exists('getCustomerApiConfig')) {
        echo "✅ getCustomerApiConfig function is available<br>";
    } else {
        echo "❌ getCustomerApiConfig function is NOT available<br>";
    }
    
    if (class_exists('GuzzleHttp\Client')) {
        echo "✅ Guzzle HTTP Client is available<br>";
    } else {
        echo "❌ Guzzle HTTP Client is NOT available<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Failed to include create-customer-minimal.php: " . $e->getMessage() . "<br>";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Error Details:</strong><br>";
    echo $e->getMessage() . "<br>";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<h2>4. What Was Fixed</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 Improvements Made:</h3>";
echo "<ul>";
echo "<li><strong>Added file_exists() checks:</strong> Prevents errors if files are missing</li>";
echo "<li><strong>Added error logging:</strong> Logs missing files for debugging</li>";
echo "<li><strong>Protected variable redefinition:</strong> Uses isset() to prevent variable conflicts</li>";
echo "<li><strong>Maintained conditional includes:</strong> Prevents 'already included' errors on server</li>";
echo "</ul>";

echo "<h3>📁 How it works:</h3>";
echo "<ul>";
echo "<li><strong>Server:</strong> Files already included by payment-success.php → conditionals skip re-including</li>";
echo "<li><strong>Localhost:</strong> Files might not be included → conditionals include them with proper error handling</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. Error Log Check</h2>";
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<p>If there are still issues, check the PHP error log for messages starting with:</p>";
echo "<code>create-customer-minimal.php: [filename] not found at: [path]</code>";
echo "</div>";

echo "<h2>6. Summary</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Safe Fix Applied:</strong> Improved conditional includes with proper error handling and path validation.</p>";
echo "<p><strong>🎯 Expected Result:</strong> Works on both localhost and server without conflicts.</p>";
echo "<p><strong>🔍 Key Improvement:</strong> Added file existence checks and error logging to identify missing dependencies.</p>";
echo "</div>";

?>
