<?php
/**
 * Test Localhost Path Fix
 * Verify that the path detection fix resolves the Appika upload issue
 */

echo "<h1>🔧 Localhost Path Fix Test</h1>";
echo "<p>Testing if the conditional include fix resolves the Appika upload issue on localhost...</p>";

// Auto-detect environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

echo "<h2>1. Environment Detection</h2>";
echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Current Environment:</strong> " . ($is_localhost ? 'LOCALHOST' : 'SERVER') . "<br>";
echo "<strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "<br>";
echo "<strong>File Base Path:</strong> '" . $file_base_path . "'<br>";
echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "</div>";

echo "<h2>2. Testing Fixed Includes</h2>";

// Test the fixed create-customer-minimal.php
try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/create-customer-minimal.php';
    echo "✅ create-customer-minimal.php included successfully<br>";
    
    // Test if all required functions are available
    if (function_exists('createCustomerMinimal')) {
        echo "✅ createCustomerMinimal function is available<br>";
    } else {
        echo "❌ createCustomerMinimal function is NOT available<br>";
    }
    
    if (function_exists('getCustomerApiConfig')) {
        echo "✅ getCustomerApiConfig function is available<br>";
        
        // Test API configuration
        $apiConfig = getCustomerApiConfig();
        echo "<div style='background: #e8f4fd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>API Configuration:</strong><br>";
        echo "- Endpoint: " . $apiConfig['endpoint'] . "<br>";
        echo "- Path: " . $apiConfig['path'] . "<br>";
        echo "- Key: " . substr($apiConfig['key'], 0, 20) . "...<br>";
        echo "</div>";
    } else {
        echo "❌ getCustomerApiConfig function is NOT available<br>";
    }
    
    if (class_exists('GuzzleHttp\Client')) {
        echo "✅ Guzzle HTTP Client is available<br>";
    } else {
        echo "❌ Guzzle HTTP Client is NOT available<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Failed to include create-customer-minimal.php: " . $e->getMessage() . "<br>";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Error Details:</strong><br>";
    echo $e->getMessage() . "<br>";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
    echo "</div>";
}

echo "<h2>3. Test Customer Creation Function</h2>";

if (function_exists('createCustomerMinimal')) {
    // Test with minimal data (don't actually create)
    $testData = [
        'username' => 'test_user_' . time(),
        'email' => '<EMAIL>',
        'password' => 'testpass123',
        'full_name' => 'Test User',
        'timezone' => 'UTC'
    ];
    
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>Test Data Structure:</strong><br>";
    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
    
    echo "✅ Function is ready to be called<br>";
    echo "⚠️ Skipping actual function call to avoid creating test users<br>";
} else {
    echo "❌ Cannot test - createCustomerMinimal function not available<br>";
}

echo "<h2>4. Before vs After Comparison</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🔧 What was fixed:</h3>";
echo "<ul>";
echo "<li><strong>Before:</strong> Conditional includes using <code>if (!class_exists())</code> and <code>if (!function_exists())</code></li>";
echo "<li><strong>Problem:</strong> These conditions could fail on localhost, preventing required files from loading</li>";
echo "<li><strong>After:</strong> Direct includes without conditions</li>";
echo "<li><strong>Result:</strong> All required dependencies are guaranteed to load</li>";
echo "</ul>";

echo "<h3>📁 Files that are now always included:</h3>";
echo "<ul>";
echo "<li><code>vendor/autoload.php</code> - Guzzle HTTP client</li>";
echo "<li><code>functions/server.php</code> - Database connection</li>";
echo "<li><code>functions/customer-data-service.php</code> - Customer data functions</li>";
echo "<li><code>config/api-config.php</code> - Appika API configuration</li>";
echo "</ul>";
echo "</div>";

echo "<h2>5. Testing Path Resolution</h2>";
$test_paths = [
    'vendor/autoload.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php',
    'config/api-config.php' => $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/config/api-config.php',
    'functions/server.php' => __DIR__ . '/server.php',
    'functions/customer-data-service.php' => __DIR__ . '/customer-data-service.php'
];

foreach ($test_paths as $name => $path) {
    $exists = file_exists($path);
    $status = $exists ? '✅' : '❌';
    echo "$status <strong>$name:</strong> $path<br>";
}

echo "<h2>6. Next Steps</h2>";
echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>To test the fix:</h3>";
echo "<ol>";
echo "<li>Complete a purchase on localhost</li>";
echo "<li>Check if the customer is created in Appika API</li>";
echo "<li>Look for any error messages in the browser or logs</li>";
echo "<li>Verify that all required functions are working</li>";
echo "</ol>";

echo "<h3>If the issue persists:</h3>";
echo "<ul>";
echo "<li>Check PHP error logs for any remaining include errors</li>";
echo "<li>Verify network connectivity from localhost to Appika API</li>";
echo "<li>Test with a simple API call to confirm connectivity</li>";
echo "</ul>";
echo "</div>";

echo "<h2>7. Summary</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>✅ Fix Applied:</strong> Removed conditional includes that were causing dependency loading issues on localhost.</p>";
echo "<p><strong>🎯 Expected Result:</strong> Appika customer upload should now work on localhost just like it does on the server.</p>";
echo "<p><strong>🔍 Root Cause:</strong> The conditional checks (<code>if (!class_exists())</code>) were preventing required files from being included on localhost, causing the Appika API functions to be unavailable.</p>";
echo "</div>";

?>
